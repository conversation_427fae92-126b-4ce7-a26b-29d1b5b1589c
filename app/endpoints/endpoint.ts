import type { EndpointConfig } from '../config/endpoints'
import { Sender } from '../utils/sender/sender'

export class Endpoint {
    protected readonly sender: Sender

    public constructor(protected readonly config: EndpointConfig) {
        this.sender = this.createSender(config.http)
    }

    protected createSender(config: EndpointConfig['http']) {
        return new Sender(config.url, config)
    }
}
