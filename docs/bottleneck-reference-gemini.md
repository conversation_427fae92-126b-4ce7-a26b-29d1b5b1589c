# Bottleneck Library Reference

## 1. Tổng quan

Bottleneck là một thư viện lightweight và không có dependency, dùng để quản lý tác vụ (Task Scheduler) và giới hạn tần suất (Rate Limiter) cho Node.js và trình duyệt. <PERSON><PERSON> giúp kiểm soát số lượng tác vụ được thực thi đồng thời và khoảng thời gian tối thiểu giữa các tác vụ.

**Mục đích chính:**

*   Ngăn chặn việc quá tải API của bên thứ ba.
*   Quản lý tài nguyên hệ thống hiệu quả hơn.
*   Đảm bảo ứng dụng hoạt động ổn định dưới tải cao.

**Use cases chính:**

*   Giới hạn số lượng request gửi đến một API.
*   Điều phối các tác vụ nặng (ví dụ: xử lý ảnh, video).
*   Quản lý kết nối đến cơ sở dữ liệu hoặc các tài nguyên chia sẻ khác.
*   Thực thi các tác vụ theo một lịch trình nhất định hoặc theo cụm (bursts).

## 2. API Reference

### 2.1. Constructor

Khởi tạo một instance mới của Bottleneck.

```typescript
import Bottleneck from 'bottleneck'

// Ví dụ cơ bản
const limiter = new Bottleneck({
  maxConcurrent: 1, // Chỉ cho phép 1 tác vụ chạy đồng thời
  minTime: 1000     // Đợi 1000ms giữa các tác vụ
})

// Với Reservoir (bể chứa)
const reservoirLimiter = new Bottleneck({
  reservoir: 50, // Số lượng tác vụ ban đầu được phép chạy
  reservoirRefreshInterval: 60 * 1000, // Làm mới reservoir mỗi 60 giây
  reservoirRefreshAmount: 50, // Số lượng tác vụ được thêm vào reservoir sau mỗi lần làm mới
  maxConcurrent: 5,
  minTime: 200
})

// Với Increase Interval (gia tăng định kỳ)
const increaseLimiter = new Bottleneck({
  reservoir: 10, // Số lượng tác vụ ban đầu
  reservoirIncreaseInterval: 1000, // Mỗi giây
  reservoirIncreaseAmount: 2, // Thêm 2 "slot" vào reservoir
  reservoirIncreaseMaximum: 20, // Giới hạn tối đa của reservoir
  maxConcurrent: 1,
  minTime: 100
})
```

**Các tùy chọn (Options) chính:**

*   `maxConcurrent` (number | null): Số lượng tác vụ tối đa có thể chạy đồng thời. `null` nghĩa là không giới hạn. Mặc định: `null`.
*   `minTime` (number): Thời gian tối thiểu (ms) phải đợi sau khi một tác vụ bắt đầu trước khi bắt đầu tác vụ tiếp theo. Mặc định: `0`.
*   `highWater` (number | null): Số lượng tác vụ tối đa trong hàng đợi. Khi vượt quá, chiến lược (strategy) được chọn sẽ được thực thi. `null` nghĩa là không giới hạn. Mặc định: `null`.
*   `strategy` (Bottleneck.strategy): Chiến lược xử lý khi `highWater` bị vượt quá. Các giá trị có thể:
    *   `Bottleneck.strategy.LEAK`: Bỏ tác vụ cũ nhất có ưu tiên thấp nhất.
    *   `Bottleneck.strategy.OVERFLOW`: Không thêm tác vụ mới nếu hàng đợi đầy.
    *   `Bottleneck.strategy.OVERFLOW_PRIORITY`: Tương tự `LEAK` nhưng chỉ bỏ nếu tác vụ mới quan trọng hơn.
    *   `Bottleneck.strategy.BLOCK`: Chặn tất cả tác vụ mới và bỏ các tác vụ đang đợi cho đến khi `penalty` hết hạn.
    Mặc định: `Bottleneck.strategy.LEAK`.
*   `penalty` (number): Thời gian (ms) mà limiter sẽ bị chặn khi sử dụng chiến lược `BLOCK`. Mặc định: `15 * minTime` hoặc `5000` nếu `minTime` là `0`.
*   `reservoir` (number | null): Số lượng "slot" ban đầu trong bể chứa. Mỗi tác vụ tiêu thụ một slot. Nếu `reservoir` về 0, không tác vụ nào được thực thi cho đến khi reservoir được nạp lại. Mặc định: `null`.
*   `reservoirRefreshInterval` (number | null): Khoảng thời gian (ms) để làm mới `reservoir` về giá trị `reservoirRefreshAmount`. Phải là bội số của 250 (hoặc 5000 khi dùng Clustering). Mặc định: `null`.
*   `reservoirRefreshAmount` (number | null): Giá trị để đặt cho `reservoir` khi `reservoirRefreshInterval` được kích hoạt. Mặc định: `null`.
*   `reservoirIncreaseInterval` (number | null): Khoảng thời gian (ms) để tăng `reservoir` thêm `reservoirIncreaseAmount`. Phải là bội số của 250 (hoặc 5000 khi dùng Clustering). Mặc định: `null`.
*   `reservoirIncreaseAmount` (number | null): Số lượng slot được thêm vào `reservoir` khi `reservoirIncreaseInterval` được kích hoạt. Mặc định: `null`.
*   `reservoirIncreaseMaximum` (number | null): Giá trị tối đa mà `reservoir` có thể đạt được khi sử dụng `reservoirIncreaseInterval`. Mặc định: `null`.
*   `Promise` (PromiseConstructorLike): Cho phép ghi đè thư viện Promise được sử dụng. Mặc định: `Promise` (built-in).
*   `datastore` (string): `"local"` (mặc định), `"redis"`, hoặc `"ioredis"`. Dùng để kích hoạt Clustering.
*   `id` (string): ID của limiter, quan trọng khi dùng Clustering để các limiter cùng ID chia sẻ trạng thái. Mặc định: `"<no-id>"`.
*   `clearDatastore` (boolean): Khi `true` và dùng Clustering, sẽ xóa trạng thái Bottleneck hiện có trên Redis khi khởi động. Mặc định: `false`.
*   `clientOptions` (object): Tùy chọn cho Redis client.
*   `clusterNodes` (array | null): Chỉ dùng cho `ioredis` khi sử dụng Redis Cluster.
*   `timeout` (number | null): TTL (ms) cho các key trên Redis.
*   `rejectOnDrop` (boolean): Khi `true`, các job bị drop sẽ reject promise của chúng. Mặc định: `true`.
*   `trackDoneStatus` (boolean): Khi `true`, Bottleneck sẽ theo dõi trạng thái `DONE` của các job. Mặc định: `false`.

### 2.2. `submit()`

Thêm một tác vụ vào hàng đợi (phiên bản callback).

```typescript
limiter.submit(
  (arg1, arg2, callback) => {
    // Thực hiện tác vụ bất đồng bộ
    // Gọi callback(error, result) khi hoàn thành
    console.log(`Executing job with ${arg1} and ${arg2}`)
    setTimeout(() => {
      if (arg1 === 'fail') {
        callback(new Error('Failed!'))
      } else {
        callback(null, `Success with ${arg1} and ${arg2}`)
      }
    }, 500)
  },
  'hello', // arg1
  'world', // arg2
  (error, result) => { // callback chính
    if (error) {
      console.error('Submit failed:', error.message)
      return
    }
    console.log('Submit successful:', result)
  }
)

// Với options
limiter.submit(
  { id: 'my-job-1', priority: 3, weight: 2 },
  (cb) => { setTimeout(() => cb(null, 'done'), 100) },
  (err, res) => { console.log(res) }
)
```

**Lưu ý:** Ngay cả khi không có callback chính, hàm tác vụ vẫn *phải* gọi callback của nó để báo cho limiter biết tác vụ đã hoàn thành.

### 2.3. `schedule()`

Thêm một tác vụ vào hàng đợi (phiên bản Promise và async/await).

```typescript
async function myAsyncTask(arg1, arg2) {
  console.log(`Scheduling job with ${arg1} and ${arg2}`)
  await new Promise(resolve => setTimeout(resolve, 500))
  if (arg1 === 'fail') {
    throw new Error('Scheduled task failed!')
  }
  return `Scheduled task success with ${arg1} and ${arg2}`
}

limiter.schedule(() => myAsyncTask('data1', 'data2'))
  .then(result => console.log(result))
  .catch(error => console.error(error.message))

// Với options
limiter.schedule({ id: 'my-scheduled-job-1', priority: 7 }, () => myAsyncTask('options', 'test'))
  .then(result => console.log(result))
  .catch(error => console.error(error.message))

// Với async/await
try {
  const result = await limiter.schedule(() => myAsyncTask('await', 'example'))
  console.log('Async/await result:', result)
} catch (e) {
  console.error('Async/await error:', e.message)
}
```

### 2.4. `wrap()`

Bọc một hàm trả về Promise. Hàm được bọc sẽ tự động được giới hạn tần suất.

```typescript
async function originalFunction(name) {
  await new Promise(resolve => setTimeout(resolve, 300))
  return `Wrapped function called for ${name}`
}

const wrappedFunction = limiter.wrap(originalFunction)

wrappedFunction('Alice')
  .then(result => console.log(result))
  .catch(error => console.error(error.message))

// Với options
wrappedFunction.withOptions({ id: 'wrapped-job-bob', priority: 1 }, 'Bob')
  .then(result => console.log(result))
  .catch(error => console.error(error.message))
```

### 2.5. Tùy chọn Job (Job Options)

Các hàm `submit()`, `schedule()`, và `wrap().withOptions()` đều chấp nhận một đối tượng options cho job:

```typescript
const jobOptions = {
  id: 'unique-job-id', // String: ID của job, hữu ích cho debugging. Mặc định: "<no-id>".
  priority: 5,         // Number (0-9): Ưu tiên của job (0 là cao nhất). Mặc định: 5.
                       // Quan trọng: `maxConcurrent` phải thấp để ưu tiên có hiệu lực.
  weight: 1,           // Number (>=0): "Trọng số" của job, ảnh hưởng đến `maxConcurrent` và `reservoir`. Mặc định: 1.
  expiration: null     // Number (ms) | null: Thời gian tối đa cho phép job hoàn thành. Mặc định: null (không giới hạn).
}
```

### 2.6. `updateSettings()`

Cập nhật các tùy chọn của limiter. Các tùy chọn giống như của constructor.

```typescript
await limiter.updateSettings({ minTime: 500, maxConcurrent: 2 })
console.log('Limiter settings updated')
```
**Lưu ý:** Thay đổi không ảnh hưởng đến các job đã ở trạng thái `SCHEDULED`.

### 2.7. `currentReservoir()`

Trả về một Promise chứa giá trị hiện tại của reservoir.

```typescript
const reservoirValue = await limiter.currentReservoir()
console.log('Current reservoir:', reservoirValue)
```

### 2.8. `incrementReservoir()`

Tăng giá trị reservoir thêm một lượng nhất định. Trả về một Promise chứa giá trị mới của reservoir.

```typescript
const newReservoirValue = await limiter.incrementReservoir(10)
console.log('New reservoir after increment:', newReservoirValue)
```

### 2.9. `stop()`

Dừng limiter một cách an toàn. Ngăn không cho job mới được thêm và đợi các job đang `EXECUTING` hoàn thành.

```typescript
await limiter.stop({ dropWaitingJobs: true }) // Mặc định dropWaitingJobs là true
console.log('Limiter stopped and waiting jobs dropped.')

// Hoặc đợi tất cả job hoàn thành
await limiter.stop({ dropWaitingJobs: false })
console.log('Limiter stopped and all jobs completed.')
```
**Tùy chọn cho `stop()`:**
*   `dropWaitingJobs` (boolean): `true` (mặc định) sẽ bỏ các job `RECEIVED`, `QUEUED`, `RUNNING`. `false` sẽ cho phép chúng hoàn thành.
*   `dropErrorMessage` (string): Thông báo lỗi khi job bị drop.
*   `enqueueErrorMessage` (string): Thông báo lỗi khi cố thêm job sau khi `stop()` được gọi.

### 2.10. `chain()`

Nối một limiter với một limiter khác. Các tác vụ sẵn sàng thực thi từ limiter hiện tại sẽ được thêm vào limiter được chain.

```typescript
const globalLimiter = new Bottleneck({ maxConcurrent: 10, minTime: 100 })
const specificLimiter = new Bottleneck({ maxConcurrent: 2, minTime: 500 })

specificLimiter.chain(globalLimiter)

// Tác vụ thêm vào specificLimiter sẽ tuân theo cả giới hạn của specificLimiter và globalLimiter.

// Để bỏ chain:
specificLimiter.chain(null)
```

### 2.11. Các phương thức kiểm tra trạng thái

*   `queued(priority?: number)`: Trả về số lượng job đang `QUEUED` (có thể lọc theo `priority`).
    ```typescript
    console.log('Queued jobs (priority 5):', limiter.queued(5))
    console.log('Total queued jobs:', limiter.queued())
    ```
*   `running()`: Trả về Promise chứa tổng trọng số của các job `RUNNING` và `EXECUTING` trong Cluster.
    ```typescript
    const runningJobsWeight = await limiter.running()
    console.log('Total weight of running/executing jobs:', runningJobsWeight)
    ```
*   `done()`: Trả về Promise chứa tổng trọng số của các job `DONE` trong Cluster.
    ```typescript
    const doneJobsWeight = await limiter.done()
    console.log('Total weight of done jobs:', doneJobsWeight)
    ```
*   `empty()`: Trả về boolean cho biết có job nào `RECEIVED` hoặc `QUEUED` không.
    ```typescript
    if (limiter.empty()) {
      console.log('Limiter queue is empty.')
    }
    ```
*   `check(weight?: number)`: Trả về Promise<boolean> cho biết một job mới (với `weight` tùy chọn) có được thực thi ngay lập tức không.
    ```typescript
    const canRunNow = await limiter.check(1)
    console.log('Can a job with weight 1 run now?', canRunNow)
    ```
*   `jobStatus(id: string)`: Trả về trạng thái của job với ID đã cho (ví dụ: `QUEUED`, `RUNNING`). Trả về `null` nếu không tìm thấy.
    ```typescript
    console.log('Status of job "my-job-1":', limiter.jobStatus('my-job-1'))
    ```
*   `jobs(status?: string)`: Trả về mảng các ID job với trạng thái đã cho. Không truyền `status` sẽ trả về tất cả ID đã biết.
    ```typescript
    console.log('Running job IDs:', limiter.jobs('RUNNING'))
    ```
*   `counts()`: Trả về một object chứa số lượng job cho mỗi trạng thái.
    ```typescript
    console.log('Job counts by status:', limiter.counts())
    // { RECEIVED: 0, QUEUED: 0, RUNNING: 0, EXECUTING: 0, DONE: 0 } (nếu trackDoneStatus: true)
    ```

### 2.12. Clustering Methods (Khi `datastore` là `redis` hoặc `ioredis`)

*   `ready()`: Trả về Promise giải quyết khi limiter kết nối thành công với Redis.
    ```typescript
    limiter.on('error', (err) => console.error('Connection error:', err)) // Quan trọng: bắt lỗi kết nối
    await limiter.ready()
    console.log('Limiter connected to Redis.')
    ```
*   `publish(message: string)`: Phát một tin nhắn (string) đến tất cả limiter trong Cluster. Trả về Promise.
    ```typescript
    limiter.on('message', (msg) => console.log('Received broadcast:', msg))
    await limiter.publish(JSON.stringify({ event: 'user_updated', userId: 123 }))
    ```
*   `disconnect(flush?: boolean)`: Ngắt kết nối khỏi Redis.
    ```typescript
    await limiter.disconnect()
    console.log('Disconnected from Redis.')
    ```
*   `clients()`: Trả về object chứa các Redis client đang được sử dụng (`{ client, subscriber }`).
    ```typescript
    const redisClients = limiter.clients()
    // redisClients.client, redisClients.subscriber
    ```
*   `clusterQueued()`: Trả về Promise chứa số lượng job `QUEUED` trong toàn bộ Cluster.
    ```typescript
    const totalClusterQueued = await limiter.clusterQueued()
    console.log('Total queued jobs in cluster:', totalClusterQueued)
    ```

## 3. Lifecycle của Job

Một job trong Bottleneck trải qua các giai đoạn sau:

1.  **RECEIVED**: Job mới được thêm vào limiter. Bottleneck kiểm tra xem có thể chấp nhận vào hàng đợi không.
2.  **QUEUED**: Bottleneck đã chấp nhận job, nhưng chưa thể xác định thời điểm chạy chính xác do phụ thuộc vào các job trước đó.
3.  **RUNNING**: Job không còn trong hàng đợi, nó sẽ được thực thi sau một khoảng trễ được tính toán dựa trên `minTime`.
4.  **EXECUTING**: Code của job đang được thực thi.
5.  **DONE**: Job đã hoàn thành. (Mặc định không theo dõi, cần `trackDoneStatus: true`).

## 4. Events

Bottleneck phát ra nhiều sự kiện để theo dõi hoạt động của limiter và các job.

```typescript
limiter.on('event-name', (arg1, arg2, ...) => {
  // Xử lý sự kiện
})
```

**Danh sách các sự kiện chính:**

*   `error` (error: Error): Phát ra khi có lỗi, ví dụ: exception không được bắt trong handler, lỗi mạng khi dùng Clustering.
    *   **Mục đích:** Báo lỗi nghiêm trọng.
    *   **Khi nào emit:** Khi có lỗi không mong muốn.
    *   **Sử dụng:** Log lỗi, thực hiện các hành động khắc phục.
*   `failed` (error: Error, jobInfo: JobInfo): Phát ra mỗi khi một job thất bại.
    *   **Mục đích:** Thông báo job thất bại.
    *   **Khi nào emit:** Sau khi một job thực thi và throw error, hoặc hết `expiration`.
    *   **Sử dụng:** Retry logic, logging chi tiết lỗi của job.
    *   **Thứ tự:** Sau `executing`.
*   `retry` (error: Error, jobInfo: JobInfo): Phát ra mỗi khi một job được thử lại (sau khi return một số từ handler `failed`).
    *   **Mục đích:** Thông báo job đang được thử lại.
    *   **Khi nào emit:** Ngay trước khi job được lên lịch thử lại.
    *   **Sử dụng:** Logging, theo dõi số lần thử lại.
    *   **Thứ tự:** Sau `failed` (nếu `failed` handler return số ms để retry).
*   `empty` (): Phát ra khi `limiter.empty()` trở thành `true` (không có job `RECEIVED` hoặc `QUEUED`).
    *   **Mục đích:** Báo hiệu hàng đợi (local) trống.
    *   **Khi nào emit:** Khi job cuối cùng rời khỏi trạng thái `QUEUED` hoặc `RECEIVED`.
    *   **Sử dụng:** Kích hoạt các tác vụ dọn dẹp, thay đổi trạng thái ứng dụng.
*   `idle` (): Phát ra khi `limiter.empty()` là `true` VÀ `limiter.running()` là `0` (không có job nào đang chạy trong toàn bộ cluster).
    *   **Mục đích:** Báo hiệu limiter hoàn toàn rảnh rỗi.
    *   **Khi nào emit:** Khi hàng đợi trống và không có job nào đang thực thi.
    *   **Sử dụng:** Tắt tài nguyên, báo cáo trạng thái.
*   `dropped` (jobInfo: JobInfo): Phát ra khi một chiến lược (strategy) được kích hoạt và một job bị loại bỏ.
    *   **Mục đích:** Thông báo job bị drop do `highWater` và strategy.
    *   **Khi nào emit:** Khi thêm job mới làm vượt `highWater` và strategy quyết định drop một job.
    *   **Sử dụng:** Logging, thông báo cho người dùng, điều chỉnh `highWater`.
*   `depleted` (empty: boolean): Phát ra mỗi khi `reservoir` giảm xuống 0. `empty` cho biết `limiter.empty()` có đang `true` không.
    *   **Mục đích:** Báo hiệu `reservoir` đã cạn.
    *   **Khi nào emit:** Khi một job chạy làm `reservoir` giảm xuống 0.
    *   **Sử dụng:** Theo dõi việc sử dụng `reservoir`, có thể tạm dừng thêm job.
*   `debug` (message: string, data: any): Cung cấp thông tin chi tiết về hoạt động của limiter.
    *   **Mục đích:** Hỗ trợ gỡ lỗi.
    *   **Khi nào emit:** Trong suốt quá trình hoạt động của limiter, ở nhiều điểm khác nhau.
    *   **Sử dụng:** Hiểu rõ luồng xử lý nội bộ của Bottleneck.
*   **Lifecycle Events**: `received`, `queued`, `running` (trong README ghi là `scheduled` nhưng code và các ví dụ khác là `running`), `executing`, `done`.
    *   `received` (jobInfo: JobInfo): Job vừa được submit.
    *   `queued` (jobInfo: JobInfo): Job đã được chấp nhận vào hàng đợi.
    *   `running` (jobInfo: JobInfo): Job đã được lên lịch chạy (sau `minTime` delay).
    *   `executing` (jobInfo: JobInfo): Job bắt đầu thực thi code.
    *   `done` (jobInfo: JobInfo): Job đã hoàn thành (cần `trackDoneStatus: true`).
    *   **Mục đích:** Theo dõi quá trình chuyển đổi trạng thái của job.
    *   **Khi nào emit:** Khi job chuyển từ trạng thái này sang trạng thái khác.
    *   **Thứ tự emit (cho một job thành công):** `received` -> `queued` -> `running` -> `executing` -> `done`.
    *   **Sử dụng:** Logging chi tiết, cập nhật UI, thống kê.
    *   **Lưu ý:** Các lifecycle event này không được phát ra cho các job trên limiter khác trong một Cluster (vì lý do hiệu năng).
*   `message` (message: string): (Chỉ khi dùng Clustering) Phát ra khi nhận được tin nhắn từ một limiter khác qua `limiter.publish()`.
    *   **Mục đích:** Giao tiếp giữa các instance limiter trong cluster.
    *   **Khi nào emit:** Khi một instance gọi `publish()` và instance hiện tại nhận được.
    *   **Sử dụng:** Đồng bộ hóa trạng thái tùy chỉnh, gửi thông báo.

**JobInfo Object:**

Nhiều event (như `failed`, `retry`, các lifecycle event) cung cấp một đối tượng `jobInfo` chứa thông tin về job:

```typescript
interface JobInfo {
  options: {
    id: string
    priority: number
    weight: number
    expiration: number | null
  }
  task: Function // Hàm tác vụ gốc
  args: any[]    // Các đối số truyền cho hàm tác vụ
  retryCount: number
  // ... và các thuộc tính nội bộ khác
}
```

**Thứ tự emit sự kiện (ví dụ cho một job thành công):**

1.  `received`
2.  `queued`
3.  `running`
4.  `executing`
5.  `done` (nếu `trackDoneStatus: true`)

Nếu job thất bại và được retry:

1.  `received`
2.  `queued`
3.  `running`
4.  `executing`
5.  `failed` (handler của `failed` return số ms để retry)
6.  `retry`
7.  (Sau khoảng thời gian retry) `executing` (lần 2)
8.  `done` (nếu lần 2 thành công) / hoặc `failed` (nếu lần 2 thất bại)

## 5. Best Practices và Recommended Patterns

*   **Luôn đặt `maxConcurrent` và `minTime`:** Ngay cả khi API không có giới hạn rõ ràng, việc này giúp bảo vệ hệ thống của bạn và API đích.
*   **Sử dụng ID cho Jobs:** Đặt `id` cho các job giúp việc debug và theo dõi dễ dàng hơn, đặc biệt với event `debug` và các hàm như `jobStatus()`.
*   **Xử lý lỗi cẩn thận:** Luôn lắng nghe sự kiện `error` trên limiter. Trong các hàm tác vụ, bắt lỗi và gọi callback/reject Promise một cách thích hợp.
*   **Sử dụng `expiration` cho jobs:** Đặc biệt quan trọng trong môi trường clustering hoặc khi các job có thể bị treo. Điều này giúp giải phóng tài nguyên.
*   **Đảm bảo job hoàn thành:** Khi dùng `submit()`, hàm tác vụ *phải* gọi callback. Khi dùng `schedule()` hoặc `wrap()`, Promise *phải* resolve hoặc reject. Nếu không, job sẽ bị kẹt và chiếm một slot `maxConcurrent`.
*   **Hiểu rõ Reservoir Intervals:** Đây là tính năng nâng cao. Đọc kỹ cảnh báo trong tài liệu chính thức, đặc biệt là việc nó không thay thế `minTime`/`maxConcurrent` và có thể gây ra burst lớn nếu không cẩn thận. Luôn dùng `minTime` và/hoặc `maxConcurrent` cùng với reservoir để làm mượt tải.
*   **Sử dụng `chain()` cho giới hạn đa cấp:** Khi cần áp dụng nhiều bộ quy tắc giới hạn (ví dụ: giới hạn theo user và giới hạn toàn cục).
*   **Clustering:**
    *   Luôn đặt `id` cho limiter khi dùng clustering.
    *   Sử dụng `expiration` cho tất cả các job trong cluster để xử lý trường hợp client bị crash.
    *   Lắng nghe sự kiện `error` trên limiter và cả trên `Connection` object (nếu tạo thủ công) để bắt lỗi kết nối Redis.
    *   Hiểu rằng hàng đợi (queue) là local cho mỗi instance. Các tính năng như ưu tiên, `highWater` hoạt động trên queue local.
*   **`trackDoneStatus: true`:** Chỉ bật nếu bạn thực sự cần theo dõi các job đã `DONE`. Nó tốn thêm bộ nhớ.
*   **`Group` cho nhiều limiter động:** Sử dụng `Bottleneck.Group` khi cần quản lý động nhiều limiter (ví dụ: giới hạn theo IP người dùng, theo ID khách hàng). Lắng nghe sự kiện `created` của Group để cấu hình các limiter mới được tạo.
*   **`Batcher` cho API hỗ trợ batch:** Nếu API cho phép gộp nhiều thao tác thành một request, `Bottleneck.Batcher` có thể giúp tối ưu hóa.

## 6. Common Pitfalls và Cách tránh

*   **Quên gọi callback/resolve Promise:** Dẫn đến job bị treo, chiếm slot `maxConcurrent` và làm limiter ngừng xử lý job mới.
    *   **Cách tránh:** Luôn đảm bảo mọi nhánh code trong job đều dẫn đến việc gọi callback hoặc resolve/reject Promise. Sử dụng `try...catch...finally` nếu cần.
*   **Hiểu lầm về `minTime`:** `minTime` là khoảng thời gian *sau khi một job bắt đầu* trước khi job tiếp theo có thể bắt đầu, không phải là khoảng thời gian *giữa khi một job kết thúc và job tiếp theo bắt đầu*.
*   **Burst không kiểm soát với Reservoir:** Nếu `reservoir` được làm mới/tăng lên một lượng lớn và không có `minTime`/`maxConcurrent` đủ nhỏ, một lượng lớn job có thể được khởi chạy cùng lúc.
    *   **Cách tránh:** Luôn kết hợp reservoir với `minTime` và/hoặc `maxConcurrent` để kiểm soát tốc độ thực thi.
*   **Không xử lý sự kiện `error`:** Bỏ qua các lỗi nghiêm trọng có thể khiến ứng dụng hoạt động không ổn định.
    *   **Cách tránh:** Luôn có `limiter.on('error', (err) => { ... })`.
*   **Sử dụng `priority` mà không có `maxConcurrent` thấp:** Nếu `maxConcurrent` cao hoặc không giới hạn, các job thường được thực thi ngay lập tức, làm cho hàng đợi ngắn hoặc không có, khiến `priority` không có tác dụng.
    *   **Cách tránh:** Đặt `maxConcurrent` ở một giá trị hợp lý để hàng đợi có thể hình thành.
*   **Binding `this` khi truyền method của object:**
    ```typescript
    // SAI
    limiter.schedule(myObject.myMethod)
    // ĐÚNG
    limiter.schedule(() => myObject.myMethod())
    // HOẶC
    limiter.schedule(myObject.myMethod.bind(myObject))
    ```
*   **Không ngắt kết nối (`disconnect()`):** Khi sử dụng Reservoir Intervals hoặc Clustering, limiter có thể giữ process Node.js sống.
    *   **Cách tránh:** Gọi `limiter.disconnect()` khi không cần dùng limiter nữa, đặc biệt trước khi thoát ứng dụng hoặc trong các hàm dọn dẹp. (Tuy nhiên, tài liệu nói rằng không cần gọi `disconnect()` để process Node.js thoát, nhưng với reservoir intervals thì cần để GC có thể dọn dẹp).

## 7. Performance Considerations

*   **Clustering Latency:** Độ trễ mạng giữa Node.js và Redis ảnh hưởng đến thời gian thực tế. Bottleneck tối ưu bằng cách chỉ thực hiện một lệnh gọi Redis cho mỗi chuyển đổi lifecycle. Đặt Redis server gần application server.
*   **`trackDoneStatus`:** Bật tính năng này (`true`) sẽ tiêu tốn thêm bộ nhớ để lưu trữ trạng thái của các job đã hoàn thành. Chỉ bật khi cần thiết.
*   **Số lượng limiter:** Tạo quá nhiều limiter không cần thiết có thể tiêu tốn bộ nhớ. Sử dụng `Bottleneck.Group` nếu cần quản lý động nhiều limiter, vì nó có cơ chế dọn dẹp các limiter không hoạt động.
*   **Độ phức tạp của job:** Các job nặng, tốn thời gian CPU sẽ ảnh hưởng đến thông lượng chung, độc lập với Bottleneck.
*   **Overhead của Promise:** Mặc dù nhỏ, việc sử dụng Promise (trong `schedule`, `wrap`) có một chút overhead so với callback thuần túy (`submit`). Tuy nhiên, sự tiện lợi thường đáng giá hơn.

## 8. Troubleshooting Guide

*   **Bật `debug` event:** `limiter.on('debug', (message, data) => console.log(message, data))` cung cấp log chi tiết về hoạt động nội bộ của limiter.
*   **Kiểm tra `error` event:** `limiter.on('error', console.error)` để xem các lỗi không mong muốn.
*   **Sử dụng `jobStatus()`, `jobs()`, `counts()`:** Để kiểm tra trạng thái hiện tại của các job và hàng đợi.
*   **Kiểm tra `id` của job:** Đảm bảo các job có ID duy nhất nếu bạn dựa vào đó để theo dõi.
*   **Vấn đề với Clustering:**
    *   Đảm bảo Redis server đang chạy và có thể truy cập.
    *   Kiểm tra `clientOptions` cho Redis.
    *   Đảm bảo tất cả các limiter trong cluster sử dụng cùng `id`.
    *   Kiểm tra xem các script Lua của Bottleneck có được load đúng cách trên Redis không (thường tự động).
    *   Lắng nghe sự kiện `error` trên `Connection` object nếu bạn quản lý nó thủ công.
*   **Job không chạy:**
    *   `maxConcurrent` có thể đã đạt giới hạn.
    *   `minTime` có thể đang làm trễ job.
    *   `reservoir` có thể đã cạn (bằng 0).
    *   Limiter có thể đang ở trạng thái `BLOCK` (nếu dùng chiến lược đó).
    *   Kiểm tra xem có job nào bị treo (không gọi callback/resolve) không.
*   **Job bị drop:**
    *   Hàng đợi có thể đã vượt `highWater`. Kiểm tra chiến lược (`strategy`) đang sử dụng.
    *   Nếu `rejectOnDrop: true` (mặc định), Promise của job sẽ bị reject.

## 9. Code Examples Thực Tế

### 9.1. Giới hạn API Request cơ bản

```typescript
import Bottleneck from 'bottleneck'
import fetch from 'node-fetch' // Giả sử dùng node-fetch

const apiClientLimiter = new Bottleneck({
  maxConcurrent: 5, // Tối đa 5 request đồng thời
  minTime: 200      // Đợi ít nhất 200ms giữa các request
})

apiClientLimiter.on('failed', async (error, jobInfo) => {
  console.warn(`Job ${jobInfo.options.id} failed with ${error.message}. Retries left: ${jobInfo.retryCount}`)
  if (jobInfo.retryCount < 2) { // Thử lại tối đa 2 lần
    return 250 * (jobInfo.retryCount + 1) // Thời gian chờ tăng dần
  }
})

apiClientLimiter.on('retry', (error, jobInfo) => {
  console.log(`Retrying job ${jobInfo.options.id}...`)
})

async function fetchData(endpoint) {
  const response = await fetch(`https://api.example.com/${endpoint}`)
  if (!response.ok) {
    throw new Error(`API request failed with status ${response.status}`)
  }
  return response.json()
}

const wrappedFetchData = apiClientLimiter.wrap(fetchData)

async function getAllUsers() {
  try {
    const users = await wrappedFetchData.withOptions({ id: 'get-users' }, 'users')
    console.log('Fetched users:', users.length)
  } catch (e) {
    console.error('Failed to fetch users:', e.message)
  }
}

async function getProductDetails(productId) {
  try {
    const product = await wrappedFetchData.withOptions({ id: `get-product-${productId}` }, `products/${productId}`)
    console.log(`Fetched product ${productId}:`, product.name)
  } catch (e) {
    console.error(`Failed to fetch product ${productId}:`, e.message)
  }
}

getAllUsers()
getProductDetails('prod_123')
getProductDetails('prod_456')
// ... nhiều request khác
```

### 9.2. Sử dụng Reservoir cho giới hạn theo "burst"

Giả sử một API cho phép 100 request mỗi phút, và bạn muốn thực hiện các request theo cụm.

```typescript
import Bottleneck from 'bottleneck'

const burstLimiter = new Bottleneck({
  reservoir: 100, // 100 requests ban đầu
  reservoirRefreshInterval: 60 * 1000, // Làm mới mỗi phút
  reservoirRefreshAmount: 100, // Đặt lại reservoir thành 100
  maxConcurrent: 10, // Chạy tối đa 10 request cùng lúc để không quá tải ngay lập tức
  minTime: 100       // Và vẫn có khoảng nghỉ nhỏ giữa các request trong burst
})

async function processItem(item) {
  console.log(`Processing item ${item.id} at ${new Date().toISOString()}`)
  await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 50)) // Giả lập công việc
  return `Item ${item.id} processed`
}

const itemsToProcess = Array.from({ length: 250 }, (_, i) => ({ id: i + 1 }))

itemsToProcess.forEach(item => {
  burstLimiter.schedule({ id: `item-${item.id}` }, () => processItem(item))
    .then(result => console.log(result))
    .catch(error => console.error(`Error processing item ${item.id}:`, error))
})

// Theo dõi reservoir
setInterval(async () => {
  const reservoir = await burstLimiter.currentReservoir()
  console.log(`Current reservoir: ${reservoir}, Queued: ${burstLimiter.queued()}`)
}, 5000)
```

### 9.3. Quản lý nhiều user với `Bottleneck.Group`

Mỗi user có giới hạn request riêng.

```typescript
import Bottleneck from 'bottleneck'

const userRateLimiterGroup = new Bottleneck.Group({
  maxConcurrent: 3, // Mỗi user tối đa 3 request đồng thời
  minTime: 1000,    // Mỗi user đợi 1s giữa các request
  timeout: 10 * 60 * 1000 // Limiter của user sẽ bị xóa sau 10 phút không hoạt động
})

userRateLimiterGroup.on('created', (limiter, key) => {
  console.log(`New limiter created for user: ${key}`)
  limiter.on('error', (err) => console.error(`Error in limiter for user ${key}:`, err))
})

async function handleUserRequest(userId, requestData) {
  console.log(`User ${userId} making request: ${requestData.action}`)
  // Giả lập xử lý request
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
  return `User ${userId} request '${requestData.action}' completed.`
}

// User 1
userRateLimiterGroup.key('user-123').schedule(() => handleUserRequest('user-123', { action: 'getProfile' }))
  .then(console.log).catch(console.error)
userRateLimiterGroup.key('user-123').schedule(() => handleUserRequest('user-123', { action: 'updateSettings' }))
  .then(console.log).catch(console.error)

// User 2
userRateLimiterGroup.key('user-456').schedule(() => handleUserRequest('user-456', { action: 'getMessages' }))
  .then(console.log).catch(console.error)

// User 1 gửi nhiều request liên tục
for (let i = 0; i < 5; i++) {
  userRateLimiterGroup.key('user-123').schedule(() => handleUserRequest('user-123', { action: `action-${i}` }))
    .then(console.log).catch(console.error)
}
```

## 10. Các mục khác (nếu cần)

### 10.1. So sánh với các thư viện khác (ví dụ: `async.queue`, `p-limit`)

*   **`async.queue`**: Một phần của thư viện `async` lớn hơn, cung cấp hàng đợi với concurrency control. Bottleneck chuyên biệt hơn cho rate limiting và có các tính năng nâng cao như reservoir, strategies, clustering.
*   **`p-limit`**: Thư viện rất nhỏ gọn chỉ tập trung vào việc giới hạn số lượng promise chạy đồng thời. Bottleneck cung cấp nhiều tính năng hơn như `minTime`, `reservoir`, `priority`, `strategies`, và clustering.

Bottleneck phù hợp hơn khi cần các kịch bản rate limiting phức tạp, quản lý burst, hoặc giới hạn trên nhiều process/server.

### 10.2. Khả năng tương thích

*   **Node.js**: Yêu cầu Node 6+. Có bản build ES5 (`require("bottleneck/es5")`) cho các môi trường cũ hơn.
*   **Trình duyệt**: Hoạt động tốt trên các trình duyệt hiện đại.

---

Tài liệu này được tạo dựa trên README của Bottleneck (phiên bản 2.x) và các thông tin liên quan. Luôn tham khảo tài liệu chính thức để có thông tin cập nhật và chi tiết nhất.
