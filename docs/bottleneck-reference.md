# Bottleneck Library - Comprehensive Reference Guide

## Tổng quan

**Bottleneck** là một thư viện JavaScript lightweight và zero-dependency đư<PERSON>c thiết kế để thực hiện Task Scheduling và Rate Limiting cho Node.js và browser. Thư viện này được phát triển bởi Simon <PERSON>din và hiện tại đang ở phiên bản 2.19.5.

### Đặc điểm chính
- **Zero dependencies**: <PERSON>hông phụ thuộc vào thư viện bên ngoài
- **Lightweight**: <PERSON><PERSON><PERSON> thước nhỏ, hiệu suất cao
- **Battle-tested**: Đ<PERSON> được sử dụng rộng rãi trong production
- **Clustering support**: Hỗ trợ rate limiting qua nhiều Node.js instances sử dụng Redis
- **Promise & Callback support**: Hỗ trợ cả async/await và callback patterns
- **TypeScript support**: Có type definitions đầy đủ

### Use Cases chính
- Rate limiting cho API calls
- Task scheduling và queue management
- Load balancing và throttling
- Distributed rate limiting qua cluster
- Batch processing với rate control

## Kiến trúc và Components

### Core Components

#### 1. Bottleneck (Main Class)
- **Mục đích**: Class chính quản lý rate limiting và job scheduling
- **Chức năng**: Tạo và quản lý queue, thực thi jobs theo rate limits
- **Lifecycle**: Khởi tạo → Nhận jobs → Queue → Schedule → Execute → Complete

#### 2. Job
- **Mục đích**: Đại diện cho một task được submit vào limiter
- **Properties**: task function, arguments, options (priority, weight, expiration, id)
- **States**: RECEIVED → QUEUED → RUNNING → EXECUTING → DONE

#### 3. States Manager
- **Mục đích**: Quản lý trạng thái của các jobs
- **Tracking**: Theo dõi số lượng jobs ở mỗi state
- **Methods**: start(), next(), remove(), jobStatus(), statusJobs()

#### 4. Queues
- **Mục đích**: Quản lý priority queues cho jobs
- **Structure**: Sử dụng DLList (Doubly Linked List) cho mỗi priority level
- **Priorities**: 0-9 (0 = highest priority, 9 = lowest priority)

#### 5. Datastore (Local/Redis)
- **LocalDatastore**: Lưu trữ state trong memory của process hiện tại
- **RedisDatastore**: Lưu trữ state trong Redis cho clustering
- **IORedisDatastore**: Hỗ trợ ioredis client

## API Reference

### Constructor

```javascript
const limiter = new Bottleneck(options);
```

#### Basic Options

| Option | Default | Description |
|--------|---------|-------------|
| `maxConcurrent` | `null` (unlimited) | Số lượng jobs tối đa có thể chạy đồng thời |
| `minTime` | `0` ms | Thời gian tối thiểu giữa các lần launch job |
| `highWater` | `null` (unlimited) | Độ dài queue tối đa trước khi strategy được kích hoạt |
| `strategy` | `Bottleneck.strategy.LEAK` | Strategy xử lý khi queue vượt quá highWater |
| `penalty` | `15 * minTime` hoặc `5000` | Thời gian penalty cho BLOCK strategy |
| `reservoir` | `null` (unlimited) | Số lượng jobs có thể execute trước khi dừng |
| `reservoirRefreshInterval` | `null` (disabled) | Interval để refresh reservoir |
| `reservoirRefreshAmount` | `null` (disabled) | Số lượng để set reservoir khi refresh |
| `reservoirIncreaseInterval` | `null` (disabled) | Interval để tăng reservoir |
| `reservoirIncreaseAmount` | `null` (disabled) | Số lượng tăng reservoir mỗi interval |
| `reservoirIncreaseMaximum` | `null` (disabled) | Giá trị tối đa của reservoir |
| `Promise` | `Promise` (built-in) | Promise library để override |

#### Clustering Options

| Option | Default | Description |
|--------|---------|-------------|
| `datastore` | `"local"` | Loại datastore: "local", "redis", "ioredis" |
| `clearDatastore` | `false` | Xóa data cũ khi khởi tạo |
| `clientOptions` | `{}` | Options cho Redis client |
| `clusterNodes` | `null` | Nodes cho Redis Cluster (ioredis only) |
| `timeout` | `null` | TTL cho Redis keys |
| `Redis` | `null` | Override Redis library |

#### Instance Options

| Option | Default | Description |
|--------|---------|-------------|
| `id` | `"<no-id>"` | ID duy nhất cho limiter (quan trọng cho clustering) |
| `rejectOnDrop` | `true` | Reject promise khi job bị drop |
| `trackDoneStatus` | `false` | Theo dõi DONE jobs (tốn memory) |

### Core Methods

#### submit()
Submit job với callback pattern:

```javascript
limiter.submit(someAsyncCall, arg1, arg2, callback);
limiter.submit(options, someAsyncCall, arg1, arg2, callback);
```

#### schedule()
Submit job với Promise pattern:

```javascript
const result = await limiter.schedule(() => myFunction(arg1, arg2));
const result = await limiter.schedule(options, () => myFunction(arg1, arg2));
```

#### wrap()
Wrap function để tự động rate limit:

```javascript
const wrapped = limiter.wrap(myFunction);
const result = await wrapped(arg1, arg2);

// Với options
const result = await wrapped.withOptions(options, arg1, arg2);
```

### Job Options

| Option | Default | Description |
|--------|---------|-------------|
| `priority` | `5` | Priority từ 0-9 (0 = highest) |
| `weight` | `1` | Trọng số của job (ảnh hưởng đến maxConcurrent và reservoir) |
| `expiration` | `null` | Timeout cho job (ms) |
| `id` | `"<no-id>"` | ID duy nhất cho job |

### Strategies

#### Bottleneck.strategy.LEAK
Drop job cũ nhất với priority thấp nhất khi queue đầy.

#### Bottleneck.strategy.OVERFLOW_PRIORITY
Drop job cũ nhất chỉ khi priority thấp hơn job mới.

#### Bottleneck.strategy.OVERFLOW
Không add job mới khi queue đầy.

#### Bottleneck.strategy.BLOCK
Block tất cả jobs trong `penalty` ms khi queue đầy.

### Reservoir Intervals

#### Refresh Interval
Tự động reset reservoir về một giá trị cố định:

```javascript
const limiter = new Bottleneck({
  reservoir: 100,
  reservoirRefreshAmount: 100,
  reservoirRefreshInterval: 60 * 1000, // 60 seconds
  maxConcurrent: 1,
  minTime: 333
});
```

#### Increase Interval
Tự động tăng reservoir theo interval:

```javascript
const limiter = new Bottleneck({
  reservoir: 40,
  reservoirIncreaseAmount: 2,
  reservoirIncreaseInterval: 1000, // 1 second
  reservoirIncreaseMaximum: 40,
  maxConcurrent: 5,
  minTime: 250
});
```

### Information Methods

#### counts()
```javascript
const counts = limiter.counts();
// { RECEIVED: 0, QUEUED: 0, RUNNING: 0, EXECUTING: 0, DONE: 0 }
```

#### jobStatus(id)
```javascript
const status = limiter.jobStatus("some-job-id");
// "QUEUED" | "RUNNING" | "EXECUTING" | "DONE" | null
```

#### jobs(status)
```javascript
const runningJobs = limiter.jobs("RUNNING");
// ["id1", "id2"]
```

#### queued(priority)
```javascript
const queuedCount = limiter.queued(); // Total queued
const priorityCount = limiter.queued(5); // Specific priority
```

#### empty()
```javascript
if (limiter.empty()) {
  // No RECEIVED or QUEUED jobs
}
```

#### running()
```javascript
const runningWeight = await limiter.running();
```

#### done()
```javascript
const doneWeight = await limiter.done();
```

#### check(weight)
```javascript
const wouldRunNow = await limiter.check(1);
```

### Management Methods

#### updateSettings(options)
```javascript
await limiter.updateSettings({
  maxConcurrent: 10,
  minTime: 100
});
```

#### currentReservoir()
```javascript
const reservoir = await limiter.currentReservoir();
```

#### incrementReservoir(amount)
```javascript
const newReservoir = await limiter.incrementReservoir(10);
```

#### stop(options)
```javascript
await limiter.stop({
  dropWaitingJobs: true,
  dropErrorMessage: "Limiter stopped",
  enqueueErrorMessage: "Cannot accept new jobs"
});
```

#### chain(otherLimiter)
```javascript
limiterA.chain(limiterG); // Jobs từ A phải follow cả A và G limits
```

## Job Lifecycle

### States và Transitions

1. **RECEIVED**: Job vừa được submit, đang chờ validation
2. **QUEUED**: Job đã được accept vào queue, chờ execution
3. **RUNNING**: Job đã được schedule, chờ delay (minTime)
4. **EXECUTING**: Job đang thực thi code
5. **DONE**: Job đã hoàn thành (chỉ khi trackDoneStatus: true)

### Lifecycle Flow

```
submit/schedule → RECEIVED → validation → QUEUED → 
priority sorting → RUNNING → minTime delay → 
EXECUTING → task execution → DONE
```

### State Transitions Events

- `received`: Job được submit
- `queued`: Job vào queue
- `scheduled`: Job được schedule để run
- `executing`: Job bắt đầu execute
- `done`: Job hoàn thành

## Events System

### Error Events

#### 'error'
```javascript
limiter.on("error", function(error) {
  // Xử lý uncaught exceptions và network errors
});
```

### Job Lifecycle Events

#### 'failed'
```javascript
limiter.on("failed", function(error, jobInfo) {
  // Job thất bại
});
```

#### 'retry'
```javascript
limiter.on("retry", function(message, jobInfo) {
  // Job được retry
});
```

### Queue Events

#### 'empty'
```javascript
limiter.on("empty", function() {
  // Queue trống (no RECEIVED or QUEUED jobs)
});
```

#### 'idle'
```javascript
limiter.on("idle", function() {
  // Queue trống và không có jobs đang chạy
});
```

#### 'dropped'
```javascript
limiter.on("dropped", function(dropped) {
  // Job bị drop bởi strategy
});
```

#### 'depleted'
```javascript
limiter.on("depleted", function(empty) {
  // Reservoir về 0
});
```

### Debug Events

#### 'debug'
```javascript
limiter.on("debug", function(message, data) {
  // Thông tin debug chi tiết
});
```

### Event Emission Order

1. `received` - Khi job được submit
2. `queued` - Khi job vào queue
3. `scheduled` - Khi job được schedule
4. `executing` - Khi job bắt đầu chạy
5. `done` - Khi job hoàn thành
6. `empty` - Khi queue trống
7. `idle` - Khi không có jobs nào đang chạy
8. `depleted` - Khi reservoir = 0

## Retries System

### Automatic Retries

```javascript
const limiter = new Bottleneck();

limiter.on("failed", async (error, jobInfo) => {
  const id = jobInfo.options.id;
  console.warn(`Job ${id} failed: ${error}`);
  
  if (jobInfo.retryCount === 0) { // Retry once
    console.log(`Retrying job ${id} in 25ms!`);
    return 25; // Retry after 25ms
  }
});

limiter.on("retry", (error, jobInfo) => {
  console.log(`Now retrying ${jobInfo.options.id}`);
});
```

### Retry Behavior

- Return số ms để retry sau khoảng thời gian đó
- Return 0 để retry ngay lập tức
- Không return gì để không retry
- Job ở state EXECUTING trong suốt quá trình retry
- Retry count được track trong jobInfo.retryCount

## Group Feature

### Tạo Group

```javascript
const group = new Bottleneck.Group(options);
```

### Sử dụng Group

```javascript
// Tạo hoặc lấy limiter cho key
const limiter = group.key("***********");

// Sử dụng như limiter bình thường
limiter.schedule(() => processRequest());
```

### Group Methods

#### key(str)
```javascript
const limiter = group.key("user-123");
```

#### on("created")
```javascript
group.on("created", (limiter, key) => {
  console.log(`New limiter created for key: ${key}`);
  
  // Setup limiter
  limiter.on("error", (err) => {
    // Handle errors
  });
});
```

#### updateSettings(options)
```javascript
group.updateSettings({ minTime: 500 });
```

#### deleteKey(str)
```javascript
group.deleteKey("user-123");
```

#### keys()
```javascript
const allKeys = group.keys();
```

#### limiters()
```javascript
const limiters = group.limiters();
// [{ key: "key1", limiter: <limiter> }, ...]
```

#### clusterKeys()
```javascript
const clusterKeys = await group.clusterKeys();
```

## Batching Feature

### Tạo Batcher

```javascript
const batcher = new Bottleneck.Batcher({
  maxTime: 1000,  // Flush sau 1 giây
  maxSize: 10     // Flush khi có 10 items
});
```

### Sử dụng Batcher

```javascript
batcher.on("batch", (batch) => {
  console.log(batch); // ["item1", "item2", ...]
  
  // Xử lý batch
  processBatch(batch);
});

// Add items
batcher.add("item1");
batcher.add("item2");
```

### Batcher Options

| Option | Default | Description |
|--------|---------|-------------|
| `maxTime` | `null` | Thời gian tối đa chờ trước khi flush |
| `maxSize` | `null` | Số lượng items tối đa trước khi flush |

## Clustering

### Cấu hình Redis Clustering

```javascript
const limiter = new Bottleneck({
  id: "my-app-limiter",
  datastore: "redis",
  clearDatastore: false,
  clientOptions: {
    host: "127.0.0.1",
    port: 6379
  },
  
  // Rate limiting settings
  maxConcurrent: 5,
  minTime: 500
});
```

### Redis Cluster Support

```javascript
const limiter = new Bottleneck({
  id: "my-app-limiter",
  datastore: "ioredis",
  clusterNodes: [
    { host: "127.0.0.1", port: 7000 },
    { host: "127.0.0.1", port: 7001 }
  ],
  clientOptions: {
    // ioredis cluster options
  }
});
```

### Clustering Methods

#### ready()
```javascript
await limiter.ready();
```

#### publish(message)
```javascript
limiter.on("message", (msg) => {
  console.log(msg);
});

limiter.publish("Hello cluster!");
```

#### clients()
```javascript
const { client, subscriber } = limiter.clients();
```

### Clustering Considerations

#### Shared State
- Settings được share qua cluster
- `maxConcurrent`, `minTime` apply cho toàn cluster
- Reservoir được share và atomic

#### Local vs Cluster Behavior
- Priorities chỉ local trong mỗi limiter
- Job order chỉ guaranteed trong cùng limiter
- `highWater` và strategies là per-limiter
- `empty` event chỉ cho local queue
- `idle` event cho cả cluster

#### Best Practices
- Luôn set `id` cho mỗi limiter
- Sử dụng `expiration` cho mọi job
- Listen `error` events
- Sử dụng `publish()` để communicate giữa instances

## Connection Management

### Manual Connection Control

```javascript
const connection = new Bottleneck.RedisConnection({
  clientOptions: { host: "127.0.0.1", port: 6379 }
});

const limiter1 = new Bottleneck({ connection });
const limiter2 = new Bottleneck({ connection });
const group = new Bottleneck.Group({ connection });
```

### Reusing Existing Client

```javascript
import Redis from "redis";
const client = new Redis.createClient({/* options */});

const connection = new Bottleneck.RedisConnection({
  client: client
});
```

### Disconnect

```javascript
// Individual limiters
limiter.disconnect();
group.disconnect();

// Manual connection
connection.disconnect();
```

## Performance Optimization

### Memory Management

#### Disable DONE Tracking
```javascript
const limiter = new Bottleneck({
  trackDoneStatus: false // Default, saves memory
});
```

#### Set Appropriate highWater
```javascript
const limiter = new Bottleneck({
  maxConcurrent: 5,
  highWater: 100, // Prevent memory issues
  strategy: Bottleneck.strategy.OVERFLOW
});
```

### Network Optimization

#### Minimize Redis Calls
- Bottleneck chỉ thực hiện 1 Redis call per lifecycle transition
- Sử dụng connection pooling
- Đặt Redis gần application servers

#### Heartbeat Tuning
```javascript
const limiter = new Bottleneck({
  datastore: "redis",
  heartbeatInterval: 5000 // Default for Redis
});
```

### Concurrency Tuning

#### Balanced Configuration
```javascript
const limiter = new Bottleneck({
  maxConcurrent: 10,    // Prevent overload
  minTime: 100,         // Smooth rate
  reservoir: 1000,      // Burst capacity
  reservoirRefreshInterval: 60000,
  reservoirRefreshAmount: 1000
});
```

## Error Handling

### Common Error Types

#### BottleneckError
```javascript
limiter.schedule(fn)
  .catch((error) => {
    if (error instanceof Bottleneck.BottleneckError) {
      // Bottleneck-specific errors
      console.log("Bottleneck error:", error.message);
    }
  });
```

#### Network Errors (Clustering)
```javascript
limiter.on("error", (err) => {
  if (err.code === "ECONNREFUSED") {
    // Redis connection failed
  }
});
```

### Error Recovery

#### Graceful Degradation
```javascript
const limiter = new Bottleneck({
  datastore: "redis",
  // Fallback to local if Redis fails
});

limiter.on("error", (err) => {
  console.error("Limiter error:", err);
  // Switch to backup limiter or disable rate limiting
});
```

#### Retry Logic
```javascript
limiter.on("failed", async (error, jobInfo) => {
  if (error.code === "ETIMEDOUT") {
    return 1000; // Retry after 1 second
  }
  if (jobInfo.retryCount < 3) {
    return Math.pow(2, jobInfo.retryCount) * 1000; // Exponential backoff
  }
  // Don't retry after 3 attempts
});
```

## Best Practices

### Configuration

#### Always Set IDs
```javascript
const limiter = new Bottleneck({
  id: "api-limiter-v1", // Important for clustering
  // other options...
});
```

#### Use Appropriate Strategies
```javascript
// For API rate limiting
const apiLimiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 200,
  strategy: Bottleneck.strategy.BLOCK,
  highWater: 50
});

// For background processing
const bgLimiter = new Bottleneck({
  maxConcurrent: 10,
  strategy: Bottleneck.strategy.OVERFLOW,
  highWater: 1000
});
```

### Job Management

#### Set Expirations
```javascript
limiter.schedule({
  expiration: 30000, // 30 seconds
  id: "unique-job-id"
}, () => apiCall());
```

#### Use Meaningful IDs
```javascript
limiter.schedule({
  id: `user-${userId}-action-${actionType}-${timestamp}`,
  priority: 3
}, () => processUserAction());
```

### Error Handling

#### Always Listen for Errors
```javascript
limiter.on("error", (err) => {
  logger.error("Limiter error:", err);
  // Alert monitoring system
});
```

#### Handle Job Failures
```javascript
limiter.on("failed", (error, jobInfo) => {
  logger.warn(`Job ${jobInfo.options.id} failed:`, error);
  // Decide on retry logic
});
```

### Monitoring

#### Track Performance
```javascript
limiter.on("executing", (jobInfo) => {
  metrics.increment("jobs.executing");
});

limiter.on("done", (jobInfo) => {
  metrics.increment("jobs.completed");
  metrics.timing("jobs.duration", Date.now() - jobInfo.startTime);
});

limiter.on("dropped", (dropped) => {
  metrics.increment("jobs.dropped");
});
```

#### Health Checks
```javascript
async function healthCheck() {
  const counts = limiter.counts();
  const running = await limiter.running();
  
  return {
    healthy: counts.QUEUED < 1000 && running < 50,
    metrics: { counts, running }
  };
}
```

## Common Pitfalls

### Function Binding Issues
```javascript
// ❌ Wrong - loses context
limiter.schedule(object.doSomething);

// ✅ Correct - bind context
limiter.schedule(object.doSomething.bind(object));

// ✅ Or use arrow function
limiter.schedule(() => object.doSomething());
```

### Incomplete Task Functions
```javascript
// ❌ Wrong - returns before work is done
limiter.schedule(() => {
  tasksArray.forEach(x => processTask(x));
  // Returns immediately, not waiting for async tasks
});

// ✅ Correct - wait for all work to complete
limiter.schedule(() => {
  const allTasks = tasksArray.map(x => processTask(x));
  return Promise.all(allTasks);
});
```

### Missing Callback Calls
```javascript
// ❌ Wrong - callback never called
limiter.submit(someAsyncCall, arg1, arg2, null);

// ✅ Correct - provide callback or empty function
limiter.submit(someAsyncCall, arg1, arg2, () => {});
```

### Priority Without maxConcurrent
```javascript
// ❌ Wrong - priorities won't work
const limiter = new Bottleneck({
  // maxConcurrent: null (unlimited)
});

// ✅ Correct - set maxConcurrent for priorities
const limiter = new Bottleneck({
  maxConcurrent: 5
});
```

## Troubleshooting

### Debug Information

#### Enable Debug Events
```javascript
limiter.on("debug", (message, data) => {
  console.log(`[DEBUG] ${message}`, data);
});
```

#### Check Job Status
```javascript
const status = limiter.jobStatus("my-job-id");
console.log(`Job status: ${status}`);

const counts = limiter.counts();
console.log("Queue counts:", counts);
```

### Common Issues

#### Jobs Stuck in RUNNING
- Check if task functions complete properly
- Verify callback is called in submit() jobs
- Look for uncaught exceptions

#### Memory Leaks
- Disable `trackDoneStatus` if not needed
- Set appropriate `highWater` limits
- Clean up event listeners

#### Redis Connection Issues
- Check Redis server availability
- Verify connection options
- Monitor network latency

#### Performance Problems
- Tune `heartbeatInterval` for Redis
- Optimize `maxConcurrent` and `minTime`
- Consider using multiple limiters for different job types

## Examples

### Basic Rate Limiting
```javascript
const limiter = new Bottleneck({
  maxConcurrent: 1,
  minTime: 333 // 3 requests per second
});

// Usage
const result = await limiter.schedule(() => apiCall());
```

### API with Burst Support
```javascript
const limiter = new Bottleneck({
  reservoir: 40,           // Initial burst
  reservoirRefreshAmount: 40,
  reservoirRefreshInterval: 60 * 1000, // Reset every minute
  maxConcurrent: 5,
  minTime: 250
});
```

### Distributed Rate Limiting
```javascript
const limiter = new Bottleneck({
  id: "shared-api-limiter",
  datastore: "redis",
  clientOptions: {
    host: "redis.example.com",
    port: 6379
  },
  maxConcurrent: 10,
  minTime: 100
});
```

### Multi-tier Rate Limiting
```javascript
const perSecondLimiter = new Bottleneck({
  maxConcurrent: 3,
  minTime: 333
});

const perHourLimiter = new Bottleneck({
  reservoir: 200,
  reservoirRefreshAmount: 200,
  reservoirRefreshInterval: 60 * 60 * 1000
});

perSecondLimiter.chain(perHourLimiter);
```

### Group-based Rate Limiting
```javascript
const group = new Bottleneck.Group({
  maxConcurrent: 1,
  minTime: 1000
});

group.on("created", (limiter, key) => {
  console.log(`Created limiter for ${key}`);
});

// Per-user rate limiting
const userLimiter = group.key(`user-${userId}`);
await userLimiter.schedule(() => processUserRequest());
```

## Migration Guide

### From v1 to v2

#### Constructor Changes
```javascript
// v1
const limiter = new Bottleneck(5, 1000);

// v2
const limiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 1000
});
```

#### Method Changes
```javascript
// v1
limiter.submitPriority(5, task, arg1, arg2, callback);

// v2
limiter.submit({ priority: 5 }, task, arg1, arg2, callback);
```

#### Settings Updates
```javascript
// v1
limiter.changeSettings(5, 1000);

// v2
await limiter.updateSettings({
  maxConcurrent: 5,
  minTime: 1000
});
```

## Conclusion

Bottleneck là một thư viện mạnh mẽ và linh hoạt cho rate limiting và task scheduling. Với kiến trúc modular, hỗ trợ clustering, và API đơn giản, nó phù hợp cho cả ứng dụng nhỏ và hệ thống phân tán lớn. Việc hiểu rõ lifecycle, events, và best practices sẽ giúp bạn sử dụng hiệu quả thư viện này trong production.